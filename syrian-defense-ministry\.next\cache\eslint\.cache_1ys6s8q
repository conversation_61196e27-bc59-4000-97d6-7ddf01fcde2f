[{"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\about\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\contact\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\layout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\media\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\news\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\news\\[id]\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\projects\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\structure\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Footer.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Navigation.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\FeaturedNews.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\HeroSection.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\HistoricalTimeline.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\LeadershipCarousel.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\NewsFilters.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\NewsGrid.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\StatisticsDashboard.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\i18n.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\middleware.ts": "22"}, {"size": 500, "mtime": 1749948146957, "results": "23", "hashOfConfig": "24"}, {"size": 146, "mtime": 1749948382704, "results": "25", "hashOfConfig": "24"}, {"size": 3514, "mtime": 1749993488327, "results": "26", "hashOfConfig": "24"}, {"size": 2833, "mtime": 1749995015153, "results": "27", "hashOfConfig": "24"}, {"size": 1258, "mtime": 1749949351971, "results": "28", "hashOfConfig": "24"}, {"size": 2869, "mtime": 1749994988559, "results": "29", "hashOfConfig": "24"}, {"size": 2174, "mtime": 1749993510571, "results": "30", "hashOfConfig": "24"}, {"size": 3281, "mtime": 1749997058321, "results": "31", "hashOfConfig": "24"}, {"size": 408, "mtime": 1749949594139, "results": "32", "hashOfConfig": "24"}, {"size": 2918, "mtime": 1749994959221, "results": "33", "hashOfConfig": "24"}, {"size": 2955, "mtime": 1749994933573, "results": "34", "hashOfConfig": "24"}, {"size": 8123, "mtime": 1749949669802, "results": "35", "hashOfConfig": "24"}, {"size": 5860, "mtime": 1749948217159, "results": "36", "hashOfConfig": "24"}, {"size": 9364, "mtime": 1749996636224, "results": "37", "hashOfConfig": "24"}, {"size": 6558, "mtime": 1749996818188, "results": "38", "hashOfConfig": "24"}, {"size": 11144, "mtime": 1749997082570, "results": "39", "hashOfConfig": "24"}, {"size": 10450, "mtime": 1749993247579, "results": "40", "hashOfConfig": "24"}, {"size": 11127, "mtime": 1749997109327, "results": "41", "hashOfConfig": "24"}, {"size": 17122, "mtime": 1749996986915, "results": "42", "hashOfConfig": "24"}, {"size": 11469, "mtime": 1749997071442, "results": "43", "hashOfConfig": "24"}, {"size": 441, "mtime": 1749997093305, "results": "44", "hashOfConfig": "24"}, {"size": 384, "mtime": 1749948132765, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k8or59", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\contact\\page.tsx", ["112"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\media\\page.tsx", ["113"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\news\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\news\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\page.tsx", ["114"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\projects\\page.tsx", ["115"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\structure\\page.tsx", ["116"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\FeaturedNews.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\HistoricalTimeline.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\LeadershipCarousel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\NewsFilters.tsx", ["117", "118"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\NewsGrid.tsx", ["119"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\StatisticsDashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\i18n.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\middleware.ts", [], [], {"ruleId": "120", "severity": 2, "message": "121", "line": 6, "column": 9, "nodeType": null, "messageId": "122", "endLine": 6, "endColumn": 10}, {"ruleId": "120", "severity": 2, "message": "121", "line": 6, "column": 9, "nodeType": null, "messageId": "122", "endLine": 6, "endColumn": 10}, {"ruleId": "120", "severity": 2, "message": "121", "line": 7, "column": 9, "nodeType": null, "messageId": "122", "endLine": 7, "endColumn": 10}, {"ruleId": "120", "severity": 2, "message": "121", "line": 6, "column": 9, "nodeType": null, "messageId": "122", "endLine": 6, "endColumn": 10}, {"ruleId": "120", "severity": 2, "message": "121", "line": 6, "column": 9, "nodeType": null, "messageId": "122", "endLine": 6, "endColumn": 10}, {"ruleId": "123", "severity": 2, "message": "124", "line": 246, "column": 17, "nodeType": "125", "messageId": "126", "suggestions": "127"}, {"ruleId": "123", "severity": 2, "message": "124", "line": 246, "column": 59, "nodeType": "125", "messageId": "126", "suggestions": "128"}, {"ruleId": "129", "severity": 1, "message": "130", "line": 40, "column": 9, "nodeType": "131", "endLine": 131, "endColumn": 4}, "@typescript-eslint/no-unused-vars", "'t' is assigned a value but never used.", "unusedVar", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["132", "133", "134", "135"], ["136", "137", "138", "139"], "react-hooks/exhaustive-deps", "The 'allNews' array makes the dependencies of useMemo Hook (at line 157) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'allNews' in its own useMemo() Hook.", "VariableDeclarator", {"messageId": "140", "data": "141", "fix": "142", "desc": "143"}, {"messageId": "140", "data": "144", "fix": "145", "desc": "146"}, {"messageId": "140", "data": "147", "fix": "148", "desc": "149"}, {"messageId": "140", "data": "150", "fix": "151", "desc": "152"}, {"messageId": "140", "data": "153", "fix": "154", "desc": "143"}, {"messageId": "140", "data": "155", "fix": "156", "desc": "146"}, {"messageId": "140", "data": "157", "fix": "158", "desc": "149"}, {"messageId": "140", "data": "159", "fix": "160", "desc": "152"}, "replaceWithAlt", {"alt": "161"}, {"range": "162", "text": "163"}, "Replace with `&quot;`.", {"alt": "164"}, {"range": "165", "text": "166"}, "Replace with `&ldquo;`.", {"alt": "167"}, {"range": "168", "text": "169"}, "Replace with `&#34;`.", {"alt": "170"}, {"range": "171", "text": "172"}, "Replace with `&rdquo;`.", {"alt": "161"}, {"range": "173", "text": "174"}, {"alt": "164"}, {"range": "175", "text": "176"}, {"alt": "167"}, {"range": "177", "text": "178"}, {"alt": "170"}, {"range": "179", "text": "180"}, "&quot;", [10601, 10641], "\n                &quot;                &quot;", "&ldquo;", [10601, 10641], "\n                &ldquo;                &quot;", "&#34;", [10601, 10641], "\n                &#34;                &quot;", "&rdquo;", [10601, 10641], "\n                &rdquo;                &quot;", [10654, 10676], "&quot;&quot;\n              ", [10654, 10676], "&quot;&ldquo;\n              ", [10654, 10676], "&quot;&#34;\n              ", [10654, 10676], "&quot;&rdquo;\n              "]