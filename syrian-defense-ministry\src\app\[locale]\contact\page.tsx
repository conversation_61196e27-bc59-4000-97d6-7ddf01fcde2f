import { useTranslations, useLocale } from 'next-intl';
import Navigation from '@/components/layout/Navigation';
import Footer from '@/components/layout/Footer';

export default function ContactPage() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <main className="min-h-screen">
      <Navigation />
      
      {/* Page Header */}
      <section className="pt-20 pb-16 bg-deep-charcoal camo-pattern">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold text-pure-white mb-6 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'اتصل بنا' : 'Contact Us'}
            </h1>
            <p className={`text-light-gray text-lg max-w-3xl mx-auto ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'للتواصل مع وزارة الدفاع السورية والحصول على المعلومات والخدمات'
                : 'To contact the Syrian Ministry of Defense and obtain information and services'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Coming Soon Section */}
      <section className="py-16 bg-steel-gray/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-military-black/50 backdrop-blur-sm rounded-lg p-12 border border-smoke-gray/30 military-glow">
            <div className="w-24 h-24 bg-military-green rounded-full flex items-center justify-center mx-auto mb-6 military-glow-strong">
              <span className="text-3xl font-bold text-pure-white">
                {locale === 'ar' ? 'ا' : 'C'}
              </span>
            </div>
            <h2 className={`text-3xl font-bold text-pure-white mb-4 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'قريباً' : 'Coming Soon'}
            </h2>
            <p className={`text-light-gray text-lg leading-relaxed ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'نعمل حالياً على تطوير صفحة التواصل التفاعلية التي ستتضمن نماذج الاتصال ومعلومات التواصل الرسمية.'
                : 'We are currently developing an interactive contact page that will include contact forms and official contact information.'
              }
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
