'use client';

import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { CalendarIcon, ClockIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

interface FeaturedNewsItem {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  date: string;
  readTime: string;
  image: string;
  urgent: boolean;
}

export default function FeaturedNews() {
  const t = useTranslations('news');
  const locale = useLocale();

  // Mock featured news data - in real implementation, this would come from API/CMS
  const featuredNews: FeaturedNewsItem = {
    id: '1',
    title: locale === 'ar' 
      ? 'وزير الدفاع يلتقي وفداً عسكرياً رفيع المستوى لبحث التعاون الاستراتيجي'
      : 'Defense Minister Meets High-Level Military Delegation to Discuss Strategic Cooperation',
    excerpt: locale === 'ar'
      ? 'عقد وزير الدفاع اجتماعاً مهماً مع وفد عسكري رفيع المستوى لمناقشة سبل تعزيز التعاون العسكري والأمني وتبادل الخبرات في مجال التدريب والتطوير التقني'
      : 'The Defense Minister held an important meeting with a high-level military delegation to discuss ways to enhance military and security cooperation and exchange expertise in training and technical development',
    category: locale === 'ar' ? 'التعاون الدولي' : 'International Cooperation',
    date: locale === 'ar' ? '15 ديسمبر 2024' : 'December 15, 2024',
    readTime: locale === 'ar' ? '5 دقائق' : '5 min read',
    image: '/api/placeholder/800/400',
    urgent: true
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0
    }
  };

  const imageVariants = {
    hidden: { scale: 1.1, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1
    }
  };

  return (
    <section className="py-16 bg-steel-gray/20 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className={`text-3xl md:text-4xl font-bold text-pure-white mb-4 ${
            locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
          }`}>
            {locale === 'ar' ? 'الخبر المميز' : 'Featured News'}
          </h2>
          <div className="w-24 h-1 bg-military-green mx-auto rounded-full"></div>
        </div>

        {/* Featured News Card */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative"
        >
          <Link href={`/${locale}/news/${featuredNews.id}`}>
            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
              className="bg-military-black/70 backdrop-blur-sm rounded-lg overflow-hidden border border-smoke-gray/30 military-glow-strong hover:military-glow-strong cursor-pointer group"
            >
              {/* Urgent Badge */}
              {featuredNews.urgent && (
                <motion.div
                  initial={{ scale: 0, rotate: -45 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="absolute top-4 left-4 rtl:left-auto rtl:right-4 z-10 bg-warning-red text-pure-white px-3 py-1 rounded-full text-sm font-bold"
                >
                  {locale === 'ar' ? 'عاجل' : 'URGENT'}
                </motion.div>
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* Image Section */}
                <div className="relative h-64 lg:h-96 overflow-hidden">
                  <motion.div
                    variants={imageVariants}
                    transition={{ duration: 1, ease: "easeOut" }}
                    className="w-full h-full bg-gradient-to-br from-steel-gray to-smoke-gray flex items-center justify-center"
                  >
                    <div className="text-center">
                      <div className="w-20 h-20 bg-military-green rounded-full flex items-center justify-center mb-4 mx-auto military-glow">
                        <span className="text-2xl font-bold text-pure-white">
                          {locale === 'ar' ? 'خ' : 'N'}
                        </span>
                      </div>
                      <p className="text-light-gray text-sm">
                        {locale === 'ar' ? 'صورة الخبر المميز' : 'Featured News Image'}
                      </p>
                    </div>
                  </motion.div>
                  
                  {/* Overlay Gradient */}
                  <div className="absolute inset-0 bg-gradient-to-t from-military-black/50 to-transparent lg:bg-gradient-to-r lg:from-transparent lg:to-military-black/30"></div>
                </div>

                {/* Content Section */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  {/* Category and Meta */}
                  <div className="flex flex-wrap items-center gap-4 mb-4">
                    <span className="bg-military-green text-pure-white px-3 py-1 rounded-full text-sm font-semibold">
                      {featuredNews.category}
                    </span>
                    <div className="flex items-center text-light-gray text-sm space-x-4 rtl:space-x-reverse">
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <CalendarIcon className="w-4 h-4" />
                        <span>{featuredNews.date}</span>
                      </div>
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <ClockIcon className="w-4 h-4" />
                        <span>{featuredNews.readTime}</span>
                      </div>
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className={`text-2xl md:text-3xl font-bold text-pure-white mb-4 group-hover:text-bright-green transition-colors duration-300 ${
                    locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                  }`}>
                    {featuredNews.title}
                  </h3>

                  {/* Excerpt */}
                  <p className={`text-light-gray leading-relaxed mb-6 ${
                    locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                  }`}>
                    {featuredNews.excerpt}
                  </p>

                  {/* Read More Button */}
                  <motion.div
                    whileHover={{ x: locale === 'ar' ? -5 : 5 }}
                    className="flex items-center text-military-green hover:text-bright-green font-semibold transition-colors duration-200"
                  >
                    <span className="mr-2 rtl:mr-0 rtl:ml-2">
                      {t('readMore')}
                    </span>
                    <ArrowRightIcon className={`w-5 h-5 ${locale === 'ar' ? 'rotate-180' : ''}`} />
                  </motion.div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-military-green/10 rounded-full -translate-y-16 translate-x-16 blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-bright-green/10 rounded-full translate-y-12 -translate-x-12 blur-xl"></div>
            </motion.div>
          </Link>
        </motion.div>

        {/* Breaking News Ticker */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-8 bg-warning-red/20 border border-warning-red/30 rounded-lg p-4"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-4 rtl:mr-0 rtl:ml-4">
              <div className="bg-warning-red text-pure-white px-3 py-1 rounded-full text-sm font-bold">
                {locale === 'ar' ? 'أخبار عاجلة' : 'BREAKING'}
              </div>
            </div>
            <div className="flex-1">
              <motion.p
                animate={{ x: [0, -20, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className={`text-pure-white font-medium ${
                  locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                }`}
              >
                {locale === 'ar'
                  ? 'تابعوا آخر التطورات والأخبار العاجلة من وزارة الدفاع السورية على مدار الساعة'
                  : 'Follow the latest developments and breaking news from the Syrian Ministry of Defense around the clock'
                }
              </motion.p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
