{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/defence%20minister%20of%20syria/syrian-defense-ministry/src/i18n.ts"], "sourcesContent": ["import { notFound } from 'next/navigation';\nimport { getRequestConfig } from 'next-intl/server';\n\n// Can be imported from a shared config\nconst locales = ['ar', 'en'];\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locale || !locales.includes(locale)) notFound();\n\n  return {\n    locale,\n    messages: (await import(`../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,uCAAuC;AACvC,MAAM,UAAU;IAAC;IAAM;CAAK;uCAEb,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,CAAC,SAAS,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAEjD,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/defence%20minister%20of%20syria/syrian-defense-ministry/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\nimport { ReactNode } from 'react';\n\ntype Props = {\n  children: ReactNode;\n  params: Promise<{ locale: string }>;\n};\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: Props) {\n  const { locale } = await params;\n\n  // Validate that the incoming `locale` parameter is valid\n  const locales = ['ar', 'en'];\n  if (!locales.includes(locale)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n      </head>\n      <body className={`\n        min-h-screen \n        bg-deep-charcoal \n        text-pure-white \n        camo-pattern\n        ${locale === 'ar' ? 'font-arabic-primary' : 'font-english-primary'}\n      `}>\n        <NextIntlClientProvider messages={messages}>\n          {children}\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;AAQe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EACA;IACN,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,yDAAyD;IACzD,MAAM,UAAU;QAAC;QAAM;KAAK;IAC5B,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;QAC7B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC;QAAK,MAAM;QAAQ,KAAK,WAAW,OAAO,QAAQ;;0BACjD,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;;;;;;;0BAEtE,8OAAC;gBAAK,WAAW,CAAC;;;;;QAKhB,EAAE,WAAW,OAAO,wBAAwB,uBAAuB;MACrE,CAAC;0BACC,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;oBAAC,UAAU;8BAC/B;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}