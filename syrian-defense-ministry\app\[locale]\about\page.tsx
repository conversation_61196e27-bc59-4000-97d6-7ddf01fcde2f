import { useTranslations, useLocale } from 'next-intl';
import Navigation from '@/components/layout/Navigation';
import Footer from '@/components/layout/Footer';
import LeadershipCarousel from '@/components/sections/LeadershipCarousel';
import HistoricalTimeline from '@/components/sections/HistoricalTimeline';
import StatisticsDashboard from '@/components/sections/StatisticsDashboard';

export default function AboutPage() {
  const t = useTranslations('about');
  const locale = useLocale();

  return (
    <main className="min-h-screen">
      <Navigation />
      
      {/* About Section */}
      <section className="pt-20 pb-16 bg-deep-charcoal camo-pattern">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold text-pure-white mb-6 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {t('title')}
            </h1>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Mission */}
            <div className="bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow">
              <h2 className={`text-2xl font-bold text-bright-green mb-4 ${
                locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
              }`}>
                {t('mission.title')}
              </h2>
              <p className={`text-light-gray leading-relaxed ${
                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
              }`}>
                {t('mission.description')}
              </p>
            </div>

            {/* Vision */}
            <div className="bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow">
              <h2 className={`text-2xl font-bold text-bright-green mb-4 ${
                locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
              }`}>
                {t('vision.title')}
              </h2>
              <p className={`text-light-gray leading-relaxed ${
                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
              }`}>
                {t('vision.description')}
              </p>
            </div>

            {/* Values */}
            <div className="bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow">
              <h2 className={`text-2xl font-bold text-bright-green mb-4 ${
                locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
              }`}>
                {t('values.title')}
              </h2>
              <ul className={`space-y-2 text-light-gray ${
                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
              }`}>
                {t.raw('values.items').map((item: string, index: number) => (
                  <li key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="w-2 h-2 bg-military-green rounded-full flex-shrink-0"></span>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced About Sections */}
      <LeadershipCarousel />
      <HistoricalTimeline />
      <StatisticsDashboard />

      <Footer />
    </main>
  );
}
