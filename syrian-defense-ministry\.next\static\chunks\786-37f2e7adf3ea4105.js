"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[786],{2548:(e,t,s)=>{s.d(t,{default:()=>h});var a=s(5155),r=s(7652),i=s(3385),l=s(6874),c=s.n(l),n=s(7208),o=s(4633),x=s(8593),d=s(2771);function h(){let e=(0,r.c3)("footer"),t=(0,i.Ym)();return(0,a.jsx)("footer",{className:"bg-military-black border-t border-steel-gray/30",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse mb-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-military-green rounded-full flex items-center justify-center military-glow",children:(0,a.jsx)("span",{className:"text-pure-white font-bold text-lg",children:"ar"===t?"ود":"MD"})}),(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-pure-white ".concat("ar"===t?"font-arabic-headings":"font-english-primary"),children:"ar"===t?"وزارة الدفاع السورية":"Syrian Ministry of Defense"})})]}),(0,a.jsx)("p",{className:"text-light-gray text-sm leading-relaxed ".concat("ar"===t?"font-arabic-secondary":"font-english-secondary"),children:"ar"===t?"حماية الوطن والمواطن من خلال قوات مسلحة حديثة ومتطورة":"Protecting Nation and Citizens through modern and advanced armed forces"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-pure-white mb-4 ".concat("ar"===t?"font-arabic-headings":"font-english-primary"),children:e("quickLinks")}),(0,a.jsx)("ul",{className:"space-y-2",children:[{key:"home",href:"/".concat(t)},{key:"about",href:"/".concat(t,"/about")},{key:"news",href:"/".concat(t,"/news")},{key:"contact",href:"/".concat(t,"/contact")}].map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(c(),{href:e.href,className:"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm",children:"home"===e.key?"ar"===t?"الرئيسية":"Home":"about"===e.key?"ar"===t?"عن الوزارة":"About":"news"===e.key?"ar"===t?"الأخبار":"News":"contact"===e.key?"ar"===t?"اتصل بنا":"Contact":e.key})},e.key))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-pure-white mb-4 ".concat("ar"===t?"font-arabic-headings":"font-english-primary"),children:e("contactInfo")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 text-military-green mt-0.5 flex-shrink-0"}),(0,a.jsx)("div",{className:"text-light-gray text-sm",children:(0,a.jsx)("p",{children:"ar"===t?"دمشق، الجمهورية العربية السورية":"Damascus, Syrian Arab Republic"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-military-green flex-shrink-0"}),(0,a.jsx)("span",{className:"text-light-gray text-sm",children:"+963-11-XXXXXXX"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-military-green flex-shrink-0"}),(0,a.jsx)("span",{className:"text-light-gray text-sm",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-military-green mt-0.5 flex-shrink-0"}),(0,a.jsx)("div",{className:"text-light-gray text-sm",children:(0,a.jsx)("p",{children:"ar"===t?"الأحد - الخميس: 8:00 - 16:00":"Sunday - Thursday: 8:00 - 16:00"})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-pure-white mb-4 ".concat("ar"===t?"font-arabic-headings":"font-english-primary"),children:e("legal")}),(0,a.jsx)("ul",{className:"space-y-2 mb-6",children:[{key:"privacy",href:"/".concat(t,"/privacy")},{key:"terms",href:"/".concat(t,"/terms")},{key:"accessibility",href:"/".concat(t,"/accessibility")}].map(t=>(0,a.jsx)("li",{children:(0,a.jsx)(c(),{href:t.href,className:"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm",children:e(t.key)})},t.key))}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-semibold text-pure-white mb-3 ".concat("ar"===t?"font-arabic-headings":"font-english-primary"),children:e("socialMedia")}),(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("a",{href:"#",className:"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200","aria-label":"Facebook",children:(0,a.jsx)("span",{className:"text-xs text-pure-white font-bold",children:"f"})}),(0,a.jsx)("a",{href:"#",className:"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200","aria-label":"Twitter",children:(0,a.jsx)("span",{className:"text-xs text-pure-white font-bold",children:"\uD835\uDD4F"})}),(0,a.jsx)("a",{href:"#",className:"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200","aria-label":"YouTube",children:(0,a.jsx)("span",{className:"text-xs text-pure-white font-bold",children:"▶"})})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-steel-gray/30 mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsx)("p",{className:"text-muted-gray text-sm ".concat("ar"===t?"font-arabic-secondary":"font-english-secondary"),children:e("copyright")}),(0,a.jsx)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:(0,a.jsx)("span",{className:"text-muted-gray text-xs ".concat("ar"===t?"font-arabic-secondary":"font-english-secondary"),children:"ar"===t?"آخر تحديث: ديسمبر 2024":"Last Updated: December 2024"})})]})})]})})}},9891:(e,t,s)=>{s.d(t,{default:()=>f});var a=s(5155),r=s(2115),i=s(7652),l=s(3385),c=s(5695),n=s(6874),o=s.n(n),x=s(760),d=s(4105),h=s(8791),m=s(3418),g=s(4500),y=s(9598);function f(){let e=(0,i.c3)("navigation"),t=(0,l.Ym)(),s=(0,c.useRouter)(),n=(0,c.usePathname)(),[f,p]=(0,r.useState)(!1),[u,b]=(0,r.useState)(!1),j=[{key:"home",href:"/".concat(t)},{key:"about",href:"/".concat(t,"/about")},{key:"structure",href:"/".concat(t,"/structure")},{key:"news",href:"/".concat(t,"/news")},{key:"projects",href:"/".concat(t,"/projects")},{key:"media",href:"/".concat(t,"/media")},{key:"contact",href:"/".concat(t,"/contact")}],N=e=>{let a=n.replace("/".concat(t),"");s.push("/".concat(e).concat(a)),b(!1)};return(0,a.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-military-black/90 backdrop-blur-md border-b border-steel-gray/30",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(o(),{href:"/".concat(t),className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-military-green rounded-full flex items-center justify-center military-glow",children:(0,a.jsx)("span",{className:"text-pure-white font-bold text-lg",children:"ود"})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("h1",{className:"text-lg font-semibold text-pure-white",children:"ar"===t?"وزارة الدفاع السورية":"Syrian Ministry of Defense"})})]}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-8 rtl:space-x-reverse",children:j.map(t=>(0,a.jsx)(o(),{href:t.href,className:"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm font-medium",children:e(t.key)},t.key))}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>b(!u),className:"flex items-center space-x-2 rtl:space-x-reverse text-light-gray hover:text-bright-green transition-colors duration-200",children:[(0,a.jsx)(h.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:t.toUpperCase()}),(0,a.jsx)(m.A,{className:"w-4 h-4"})]}),(0,a.jsx)(x.N,{children:u&&(0,a.jsxs)(d.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute top-full mt-2 right-0 bg-steel-gray rounded-lg shadow-lg border border-smoke-gray/30 overflow-hidden",children:[(0,a.jsx)("button",{onClick:()=>N("ar"),className:"block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200",children:"العربية"}),(0,a.jsx)("button",{onClick:()=>N("en"),className:"block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200",children:"English"})]})})]}),(0,a.jsx)("button",{onClick:()=>p(!f),className:"lg:hidden text-light-gray hover:text-bright-green transition-colors duration-200",children:f?(0,a.jsx)(g.A,{className:"w-6 h-6"}):(0,a.jsx)(y.A,{className:"w-6 h-6"})})]})]})}),(0,a.jsx)(x.N,{children:f&&(0,a.jsx)(d.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"lg:hidden bg-steel-gray/95 backdrop-blur-md border-t border-smoke-gray/30",children:(0,a.jsx)("div",{className:"px-4 py-4 space-y-2",children:j.map(t=>(0,a.jsx)(o(),{href:t.href,onClick:()=>p(!1),className:"block px-4 py-2 text-light-gray hover:text-bright-green hover:bg-smoke-gray/30 rounded-lg transition-all duration-200",children:e(t.key)},t.key))})})})]})}}}]);