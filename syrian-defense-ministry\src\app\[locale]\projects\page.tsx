import { useTranslations, useLocale } from 'next-intl';
import Navigation from '@/components/layout/Navigation';
import Footer from '@/components/layout/Footer';

export default function ProjectsPage() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <main className="min-h-screen">
      <Navigation />
      
      {/* Page Header */}
      <section className="pt-20 pb-16 bg-deep-charcoal camo-pattern">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold text-pure-white mb-6 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'المشاريع الاستراتيجية' : 'Strategic Projects'}
            </h1>
            <p className={`text-light-gray text-lg max-w-3xl mx-auto ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'المشاريع والمبادرات الاستراتيجية لتطوير القدرات الدفاعية والعسكرية'
                : 'Strategic projects and initiatives to develop defense and military capabilities'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Coming Soon Section */}
      <section className="py-16 bg-steel-gray/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-military-black/50 backdrop-blur-sm rounded-lg p-12 border border-smoke-gray/30 military-glow">
            <div className="w-24 h-24 bg-military-green rounded-full flex items-center justify-center mx-auto mb-6 military-glow-strong">
              <span className="text-3xl font-bold text-pure-white">
                {locale === 'ar' ? 'م' : 'P'}
              </span>
            </div>
            <h2 className={`text-3xl font-bold text-pure-white mb-4 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'قريباً' : 'Coming Soon'}
            </h2>
            <p className={`text-light-gray text-lg leading-relaxed ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'نعمل حالياً على تطوير صفحة المشاريع الاستراتيجية التي ستعرض المبادرات والمشاريع الحالية والمستقبلية للوزارة.'
                : 'We are currently developing the strategic projects page that will showcase current and future initiatives and projects of the Ministry.'
              }
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
