{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/defence%20minister%20of%20syria/syrian-defense-ministry/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { \n  NewspaperIcon, \n  PhoneIcon, \n  ExclamationTriangleIcon \n} from '@heroicons/react/24/outline';\n\nexport default function HeroSection() {\n  const t = useTranslations('hero');\n  const locale = useLocale();\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.8,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0\n    }\n  };\n\n  const emblemVariants = {\n    hidden: { opacity: 0, scale: 0.8 },\n    visible: {\n      opacity: 1,\n      scale: 1\n    }\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background with enhanced camouflage pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-military-black via-deep-charcoal to-steel-gray\">\n        <div className=\"absolute inset-0 camo-pattern opacity-30\"></div>\n        \n        {/* Floating particles */}\n        <div className=\"absolute inset-0\">\n          {[...Array(20)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-1 h-1 bg-military-green/20 rounded-full\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n              }}\n              animate={{\n                y: [0, -20, 0],\n                opacity: [0.2, 0.8, 0.2],\n              }}\n              transition={{\n                duration: 3 + Math.random() * 2,\n                repeat: Infinity,\n                delay: Math.random() * 2,\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className=\"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto\"\n      >\n        {/* Ministry Emblem */}\n        <motion.div\n          variants={emblemVariants}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"mb-8 flex justify-center\"\n        >\n          <div className=\"relative\">\n            <div className=\"w-32 h-32 md:w-40 md:h-40 bg-gradient-to-br from-yellow-600 via-yellow-500 to-yellow-700 rounded-full flex items-center justify-center military-glow-strong shadow-2xl\">\n              {/* Simplified Syrian Ministry Emblem */}\n              <div className=\"text-center\">\n                <div className=\"text-2xl md:text-3xl font-bold text-military-black mb-1\">\n                  {locale === 'ar' ? 'ود' : 'MD'}\n                </div>\n                <div className=\"text-xs md:text-sm font-semibold text-military-black\">\n                  {locale === 'ar' ? 'سوريا' : 'SYRIA'}\n                </div>\n              </div>\n            </div>\n            \n            {/* Glow effect */}\n            <motion.div\n              className=\"absolute inset-0 w-32 h-32 md:w-40 md:h-40 bg-military-green/20 rounded-full blur-xl\"\n              animate={{\n                scale: [1, 1.2, 1],\n                opacity: [0.3, 0.6, 0.3],\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n          </div>\n        </motion.div>\n\n        {/* Title */}\n        <motion.h1\n          variants={itemVariants}\n          transition={{ duration: 0.6, ease: \"easeOut\" }}\n          className={`text-4xl md:text-6xl lg:text-7xl font-bold mb-4 ${\n            locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'\n          }`}\n        >\n          <span className=\"bg-gradient-to-r from-pure-white via-light-gray to-pure-white bg-clip-text text-transparent\">\n            {t('title')}\n          </span>\n        </motion.h1>\n\n        {/* Subtitle */}\n        <motion.p\n          variants={itemVariants}\n          transition={{ duration: 0.6, ease: \"easeOut\" }}\n          className={`text-xl md:text-2xl lg:text-3xl text-bright-green mb-6 font-semibold ${\n            locale === 'ar' ? 'font-arabic-primary' : 'font-english-primary'\n          }`}\n        >\n          {t('subtitle')}\n        </motion.p>\n\n        {/* Description */}\n        <motion.p\n          variants={itemVariants}\n          transition={{ duration: 0.6, ease: \"easeOut\" }}\n          className={`text-lg md:text-xl text-light-gray mb-12 max-w-4xl mx-auto leading-relaxed ${\n            locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'\n          }`}\n        >\n          {t('description')}\n        </motion.p>\n\n        {/* Call to Action Buttons */}\n        <motion.div\n          variants={itemVariants}\n          transition={{ duration: 0.6, ease: \"easeOut\" }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n        >\n          <Link\n            href={`/${locale}/news`}\n            className=\"group flex items-center space-x-3 rtl:space-x-reverse bg-military-green hover:bg-bright-green text-pure-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 military-glow hover:military-glow-strong\"\n          >\n            <NewspaperIcon className=\"w-5 h-5\" />\n            <span>{t('cta.news')}</span>\n          </Link>\n\n          <Link\n            href={`/${locale}/contact`}\n            className=\"group flex items-center space-x-3 rtl:space-x-reverse bg-transparent border-2 border-steel-gray hover:border-bright-green text-light-gray hover:text-bright-green px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105\"\n          >\n            <PhoneIcon className=\"w-5 h-5\" />\n            <span>{t('cta.contact')}</span>\n          </Link>\n\n          <Link\n            href={`/${locale}/emergency`}\n            className=\"group flex items-center space-x-3 rtl:space-x-reverse bg-warning-red hover:bg-red-700 text-pure-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-red-900/50\"\n          >\n            <ExclamationTriangleIcon className=\"w-5 h-5\" />\n            <span>{t('cta.emergency')}</span>\n          </Link>\n        </motion.div>\n      </motion.div>\n\n      {/* Bottom gradient overlay */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-deep-charcoal to-transparent\"></div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAWe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAI;QACjC,SAAS;YACP,SAAS;YACT,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAChC;gCACA,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;gCAC1B;gCACA,YAAY;oCACV,UAAU,IAAI,KAAK,MAAM,KAAK;oCAC9B,QAAQ;oCACR,OAAO,KAAK,MAAM,KAAK;gCACzB;+BAdK;;;;;;;;;;;;;;;;0BAqBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;kCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,YAAY;4BAAE,UAAU;4BAAG,MAAM;wBAAU;wBAC3C,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,OAAO,OAAO;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;0DACZ,WAAW,OAAO,UAAU;;;;;;;;;;;;;;;;;8CAMnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAC1B;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;;;;;;;;;;;;;;;;;kCAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,UAAU;wBACV,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,WAAW,CAAC,gDAAgD,EAC1D,WAAW,OAAO,yBAAyB,wBAC3C;kCAEF,cAAA,8OAAC;4BAAK,WAAU;sCACb,EAAE;;;;;;;;;;;kCAKP,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,UAAU;wBACV,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,WAAW,CAAC,qEAAqE,EAC/E,WAAW,OAAO,wBAAwB,wBAC1C;kCAED,EAAE;;;;;;kCAIL,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,UAAU;wBACV,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,WAAW,CAAC,2EAA2E,EACrF,WAAW,OAAO,0BAA0B,0BAC5C;kCAED,EAAE;;;;;;kCAIL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,WAAU;;0CAEV,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;gCACvB,WAAU;;kDAEV,8OAAC,yNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;kDAAM,EAAE;;;;;;;;;;;;0CAGX,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;gCAC1B,WAAU;;kDAEV,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,EAAE;;;;;;;;;;;;0CAGX,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC;gCAC5B,WAAU;;kDAEV,8OAAC,6OAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,8OAAC;kDAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/defence%20minister%20of%20syria/syrian-defense-ministry/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations, useLocale } from 'next-intl';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Bars3Icon, \n  XMarkIcon, \n  LanguageIcon,\n  ChevronDownIcon \n} from '@heroicons/react/24/outline';\n\nexport default function Navigation() {\n  const t = useTranslations('navigation');\n  const locale = useLocale();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLanguageOpen, setIsLanguageOpen] = useState(false);\n\n  const navigationItems = [\n    { key: 'home', href: `/${locale}` },\n    { key: 'about', href: `/${locale}/about` },\n    { key: 'structure', href: `/${locale}/structure` },\n    { key: 'news', href: `/${locale}/news` },\n    { key: 'projects', href: `/${locale}/projects` },\n    { key: 'media', href: `/${locale}/media` },\n    { key: 'contact', href: `/${locale}/contact` },\n  ];\n\n  const switchLanguage = (newLocale: string) => {\n    const currentPath = pathname.replace(`/${locale}`, '');\n    router.push(`/${newLocale}${currentPath}`);\n    setIsLanguageOpen(false);\n  };\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-military-black/90 backdrop-blur-md border-b border-steel-gray/30\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href={`/${locale}`} className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n            <div className=\"w-10 h-10 bg-military-green rounded-full flex items-center justify-center military-glow\">\n              <span className=\"text-pure-white font-bold text-lg\">ود</span>\n            </div>\n            <div className=\"hidden md:block\">\n              <h1 className=\"text-lg font-semibold text-pure-white\">\n                {locale === 'ar' ? 'وزارة الدفاع السورية' : 'Syrian Ministry of Defense'}\n              </h1>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8 rtl:space-x-reverse\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.key}\n                href={item.href}\n                className=\"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm font-medium\"\n              >\n                {t(item.key)}\n              </Link>\n            ))}\n          </div>\n\n          {/* Language Switcher & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n            {/* Language Switcher */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setIsLanguageOpen(!isLanguageOpen)}\n                className=\"flex items-center space-x-2 rtl:space-x-reverse text-light-gray hover:text-bright-green transition-colors duration-200\"\n              >\n                <LanguageIcon className=\"w-5 h-5\" />\n                <span className=\"text-sm font-medium\">{locale.toUpperCase()}</span>\n                <ChevronDownIcon className=\"w-4 h-4\" />\n              </button>\n\n              <AnimatePresence>\n                {isLanguageOpen && (\n                  <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"absolute top-full mt-2 right-0 bg-steel-gray rounded-lg shadow-lg border border-smoke-gray/30 overflow-hidden\"\n                  >\n                    <button\n                      onClick={() => switchLanguage('ar')}\n                      className=\"block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200\"\n                    >\n                      العربية\n                    </button>\n                    <button\n                      onClick={() => switchLanguage('en')}\n                      className=\"block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200\"\n                    >\n                      English\n                    </button>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"lg:hidden text-light-gray hover:text-bright-green transition-colors duration-200\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"w-6 h-6\" />\n              ) : (\n                <Bars3Icon className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"lg:hidden bg-steel-gray/95 backdrop-blur-md border-t border-smoke-gray/30\"\n          >\n            <div className=\"px-4 py-4 space-y-2\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.key}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block px-4 py-2 text-light-gray hover:text-bright-green hover:bg-smoke-gray/30 rounded-lg transition-all duration-200\"\n                >\n                  {t(item.key)}\n                </Link>\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAce,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,kBAAkB;QACtB;YAAE,KAAK;YAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ;QAAC;QAClC;YAAE,KAAK;YAAS,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;QAAC;QACzC;YAAE,KAAK;YAAa,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC;QAAC;QACjD;YAAE,KAAK;YAAQ,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;QAAC;QACvC;YAAE,KAAK;YAAY,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;QAAC;QAC/C;YAAE,KAAK;YAAS,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;QAAC;QACzC;YAAE,KAAK;YAAW,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;QAAC;KAC9C;IAED,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,SAAS,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE;QACnD,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,aAAa;QACzC,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;4BAAE,WAAU;;8CAClC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;8CAEtD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,yBAAyB;;;;;;;;;;;;;;;;;sCAMlD,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,EAAE,KAAK,GAAG;mCAJN,KAAK,GAAG;;;;;;;;;;sCAUnB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAK,WAAU;8DAAuB,OAAO,WAAW;;;;;;8DACzD,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;;;;;;;sDAG7B,8OAAC,yLAAA,CAAA,kBAAe;sDACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,8OAAC;wDACC,SAAS,IAAM,eAAe;wDAC9B,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,eAAe;wDAC9B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;8CAST,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CAET,EAAE,KAAK,GAAG;+BALN,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;AAc/B", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/defence%20minister%20of%20syria/syrian-defense-ministry/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport Link from 'next/link';\nimport { \n  MapPinIcon, \n  PhoneIcon, \n  EnvelopeIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Footer() {\n  const t = useTranslations('footer');\n  const locale = useLocale();\n\n  const quickLinks = [\n    { key: 'home', href: `/${locale}` },\n    { key: 'about', href: `/${locale}/about` },\n    { key: 'news', href: `/${locale}/news` },\n    { key: 'contact', href: `/${locale}/contact` },\n  ];\n\n  const legalLinks = [\n    { key: 'privacy', href: `/${locale}/privacy` },\n    { key: 'terms', href: `/${locale}/terms` },\n    { key: 'accessibility', href: `/${locale}/accessibility` },\n  ];\n\n  return (\n    <footer className=\"bg-military-black border-t border-steel-gray/30\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          \n          {/* Ministry Logo & Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 rtl:space-x-reverse mb-4\">\n              <div className=\"w-12 h-12 bg-military-green rounded-full flex items-center justify-center military-glow\">\n                <span className=\"text-pure-white font-bold text-lg\">\n                  {locale === 'ar' ? 'ود' : 'MD'}\n                </span>\n              </div>\n              <div>\n                <h3 className={`text-lg font-semibold text-pure-white ${\n                  locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'\n                }`}>\n                  {locale === 'ar' ? 'وزارة الدفاع السورية' : 'Syrian Ministry of Defense'}\n                </h3>\n              </div>\n            </div>\n            <p className={`text-light-gray text-sm leading-relaxed ${\n              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'\n            }`}>\n              {locale === 'ar' \n                ? 'حماية الوطن والمواطن من خلال قوات مسلحة حديثة ومتطورة'\n                : 'Protecting Nation and Citizens through modern and advanced armed forces'\n              }\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className={`text-lg font-semibold text-pure-white mb-4 ${\n              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'\n            }`}>\n              {t('quickLinks')}\n            </h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.key}>\n                  <Link\n                    href={link.href}\n                    className=\"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm\"\n                  >\n                    {link.key === 'home' ? (locale === 'ar' ? 'الرئيسية' : 'Home') :\n                     link.key === 'about' ? (locale === 'ar' ? 'عن الوزارة' : 'About') :\n                     link.key === 'news' ? (locale === 'ar' ? 'الأخبار' : 'News') :\n                     link.key === 'contact' ? (locale === 'ar' ? 'اتصل بنا' : 'Contact') : link.key}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact Information */}\n          <div>\n            <h4 className={`text-lg font-semibold text-pure-white mb-4 ${\n              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'\n            }`}>\n              {t('contactInfo')}\n            </h4>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start space-x-3 rtl:space-x-reverse\">\n                <MapPinIcon className=\"w-5 h-5 text-military-green mt-0.5 flex-shrink-0\" />\n                <div className=\"text-light-gray text-sm\">\n                  <p>{locale === 'ar' ? 'دمشق، الجمهورية العربية السورية' : 'Damascus, Syrian Arab Republic'}</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                <PhoneIcon className=\"w-5 h-5 text-military-green flex-shrink-0\" />\n                <span className=\"text-light-gray text-sm\">+963-11-XXXXXXX</span>\n              </div>\n              \n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                <EnvelopeIcon className=\"w-5 h-5 text-military-green flex-shrink-0\" />\n                <span className=\"text-light-gray text-sm\"><EMAIL></span>\n              </div>\n              \n              <div className=\"flex items-start space-x-3 rtl:space-x-reverse\">\n                <ClockIcon className=\"w-5 h-5 text-military-green mt-0.5 flex-shrink-0\" />\n                <div className=\"text-light-gray text-sm\">\n                  <p>{locale === 'ar' ? 'الأحد - الخميس: 8:00 - 16:00' : 'Sunday - Thursday: 8:00 - 16:00'}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Legal & Social */}\n          <div>\n            <h4 className={`text-lg font-semibold text-pure-white mb-4 ${\n              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'\n            }`}>\n              {t('legal')}\n            </h4>\n            <ul className=\"space-y-2 mb-6\">\n              {legalLinks.map((link) => (\n                <li key={link.key}>\n                  <Link\n                    href={link.href}\n                    className=\"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm\"\n                  >\n                    {t(link.key)}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n            {/* Social Media */}\n            <div>\n              <h5 className={`text-sm font-semibold text-pure-white mb-3 ${\n                locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'\n              }`}>\n                {t('socialMedia')}\n              </h5>\n              <div className=\"flex space-x-3 rtl:space-x-reverse\">\n                <a\n                  href=\"#\"\n                  className=\"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200\"\n                  aria-label=\"Facebook\"\n                >\n                  <span className=\"text-xs text-pure-white font-bold\">f</span>\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200\"\n                  aria-label=\"Twitter\"\n                >\n                  <span className=\"text-xs text-pure-white font-bold\">𝕏</span>\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200\"\n                  aria-label=\"YouTube\"\n                >\n                  <span className=\"text-xs text-pure-white font-bold\">▶</span>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-steel-gray/30 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className={`text-muted-gray text-sm ${\n              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'\n            }`}>\n              {t('copyright')}\n            </p>\n            \n            <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n              <span className={`text-muted-gray text-xs ${\n                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'\n              }`}>\n                {locale === 'ar' ? 'آخر تحديث: ديسمبر 2024' : 'Last Updated: December 2024'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAWe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB;YAAE,KAAK;YAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ;QAAC;QAClC;YAAE,KAAK;YAAS,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;QAAC;QACzC;YAAE,KAAK;YAAQ,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;QAAC;QACvC;YAAE,KAAK;YAAW,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;QAAC;KAC9C;IAED,MAAM,aAAa;QACjB;YAAE,KAAK;YAAW,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;QAAC;QAC7C;YAAE,KAAK;YAAS,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;QAAC;QACzC;YAAE,KAAK;YAAiB,MAAM,CAAC,CAAC,EAAE,OAAO,cAAc,CAAC;QAAC;KAC1D;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,WAAW,OAAO,OAAO;;;;;;;;;;;sDAG9B,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAW,CAAC,sCAAsC,EACpD,WAAW,OAAO,yBAAyB,wBAC3C;0DACC,WAAW,OAAO,yBAAyB;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAE,WAAW,CAAC,wCAAwC,EACrD,WAAW,OAAO,0BAA0B,0BAC5C;8CACC,WAAW,OACR,0DACA;;;;;;;;;;;;sCAMR,8OAAC;;8CACC,8OAAC;oCAAG,WAAW,CAAC,2CAA2C,EACzD,WAAW,OAAO,yBAAyB,wBAC3C;8CACC,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,GAAG,KAAK,SAAU,WAAW,OAAO,aAAa,SACtD,KAAK,GAAG,KAAK,UAAW,WAAW,OAAO,eAAe,UACzD,KAAK,GAAG,KAAK,SAAU,WAAW,OAAO,YAAY,SACrD,KAAK,GAAG,KAAK,YAAa,WAAW,OAAO,aAAa,YAAa,KAAK,GAAG;;;;;;2CAR1E,KAAK,GAAG;;;;;;;;;;;;;;;;sCAgBvB,8OAAC;;8CACC,8OAAC;oCAAG,WAAW,CAAC,2CAA2C,EACzD,WAAW,OAAO,yBAAyB,wBAC3C;8CACC,EAAE;;;;;;8CAEL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAG,WAAW,OAAO,oCAAoC;;;;;;;;;;;;;;;;;sDAI9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAG5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;sDAG5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAG,WAAW,OAAO,iCAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/D,8OAAC;;8CACC,8OAAC;oCAAG,WAAW,CAAC,2CAA2C,EACzD,WAAW,OAAO,yBAAyB,wBAC3C;8CACC,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,EAAE,KAAK,GAAG;;;;;;2CALN,KAAK,GAAG;;;;;;;;;;8CAYrB,8OAAC;;sDACC,8OAAC;4CAAG,WAAW,CAAC,2CAA2C,EACzD,WAAW,OAAO,yBAAyB,wBAC3C;sDACC,EAAE;;;;;;sDAEL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,cAAW;8DAEX,cAAA,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;8DAEtD,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,cAAW;8DAEX,cAAA,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;8DAEtD,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,cAAW;8DAEX,cAAA,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAW,CAAC,wBAAwB,EACrC,WAAW,OAAO,0BAA0B,0BAC5C;0CACC,EAAE;;;;;;0CAGL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW,CAAC,wBAAwB,EACxC,WAAW,OAAO,0BAA0B,0BAC5C;8CACC,WAAW,OAAO,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D", "debugId": null}}]}