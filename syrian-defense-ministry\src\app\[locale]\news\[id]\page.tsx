import Navigation from '@/components/layout/Navigation';
import Footer from '@/components/layout/Footer';
import Link from 'next/link';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface NewsDetailPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

export default async function NewsDetailPage({ params }: NewsDetailPageProps) {
  const { locale, id } = await params;

  return (
    <main className="min-h-screen">
      <Navigation />
      
      {/* Back Navigation */}
      <section className="pt-20 pb-8 bg-military-black/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link 
            href={`/${locale}/news`}
            className="inline-flex items-center text-military-green hover:text-bright-green transition-colors duration-200"
          >
            <ArrowLeftIcon className={`w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2 ${locale === 'ar' ? 'rotate-180' : ''}`} />
            <span className={locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}>
              {locale === 'ar' ? 'العودة إلى الأخبار' : 'Back to News'}
            </span>
          </Link>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16 bg-deep-charcoal camo-pattern">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 lg:p-12 border border-smoke-gray/30 military-glow">
            <div className="text-center mb-8">
              <div className="w-24 h-24 bg-military-green rounded-full flex items-center justify-center mx-auto mb-6 military-glow-strong">
                <span className="text-3xl font-bold text-pure-white">
                  {locale === 'ar' ? 'خ' : 'N'}
                </span>
              </div>
              <h1 className={`text-3xl md:text-4xl font-bold text-pure-white mb-4 ${
                locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
              }`}>
                {locale === 'ar' ? 'تفاصيل الخبر' : 'News Details'}
              </h1>
              <p className={`text-light-gray text-lg ${
                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
              }`}>
                {locale === 'ar'
                  ? `معرف الخبر: ${id}`
                  : `News ID: ${id}`
                }
              </p>
            </div>

            <div className="prose prose-invert max-w-none">
              <p className={`text-light-gray leading-relaxed text-lg ${
                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
              }`}>
                {locale === 'ar' 
                  ? 'نعمل حالياً على تطوير صفحات تفاصيل الأخبار التي ستعرض المحتوى الكامل للأخبار والتحديثات مع الصور والمعلومات التفصيلية.'
                  : 'We are currently developing news detail pages that will display the full content of news and updates with images and detailed information.'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
