'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion, useInView } from 'framer-motion';
import { 
  ShieldCheckIcon, 
  ClockIcon, 
  AcademicCapIcon, 
  GlobeAltIcon,
  TrophyIcon,
  UsersIcon,
  CogIcon,
  StarIcon
} from '@heroicons/react/24/outline';

interface StatisticItem {
  number: string;
  label: string;
  description: string;
}

const iconMap = {
  0: ShieldCheckIcon,
  1: GlobeAltIcon,
  2: AcademicCapIcon,
  3: ClockIcon
};

function AnimatedCounter({ value, duration = 2000 }: { value: string; duration?: number }) {
  const [displayValue, setDisplayValue] = useState('0');
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (!isInView) return;

    // Extract numeric part from value
    const numericMatch = value.match(/\d+/);
    if (!numericMatch) {
      setDisplayValue(value);
      return;
    }

    const targetNumber = parseInt(numericMatch[0]);
    const suffix = value.replace(numericMatch[0], '');
    
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentNumber = Math.floor(targetNumber * easeOutQuart);
      
      setDisplayValue(currentNumber + suffix);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isInView, value, duration]);

  return <span ref={ref}>{displayValue}</span>;
}

export default function StatisticsDashboard() {
  const t = useTranslations('about.statistics');
  const locale = useLocale();
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  const statistics: StatisticItem[] = t.raw('items');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.8,
      rotateX: -15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0
    }
  };

  const glowVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: [0.3, 0.7, 0.3]
    }
  };

  return (
    <section className="py-16 bg-military-black/50 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-center mb-4"
          >
            <TrophyIcon className="w-8 h-8 text-military-green mr-3 rtl:mr-0 rtl:ml-3" />
            <h2 className={`text-3xl md:text-4xl font-bold text-pure-white ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {t('title')}
            </h2>
          </motion.div>
          <motion.p 
            initial={{ opacity: 0, y: -10 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={`text-light-gray text-lg ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}
          >
            {t('subtitle')}
          </motion.p>
        </div>

        {/* Statistics Grid */}
        <motion.div
          ref={containerRef}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {statistics.map((stat, index) => {
            const IconComponent = iconMap[index as keyof typeof iconMap] || StarIcon;
            
            return (
              <motion.div
                key={index}
                variants={cardVariants}
                transition={{ duration: 0.8, ease: "easeOut" }}
                whileHover={{
                  scale: 1.05,
                  y: -10,
                  transition: { duration: 0.3 }
                }}
                className="relative group"
              >
                {/* Background Glow */}
                <motion.div
                  variants={glowVariants}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute inset-0 bg-military-green/20 rounded-lg blur-xl"
                />
                
                {/* Card Content */}
                <div className="relative bg-steel-gray/70 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow hover:military-glow-strong transition-all duration-300 h-full">
                  {/* Icon */}
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                    className="flex items-center justify-center mb-6"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-military-green to-bright-green rounded-full flex items-center justify-center military-glow-strong">
                      <IconComponent className="w-8 h-8 text-pure-white" />
                    </div>
                  </motion.div>

                  {/* Number */}
                  <div className="text-center mb-4">
                    <motion.div
                      className={`text-4xl md:text-5xl font-bold text-bright-green mb-2 ${
                        locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                      }`}
                    >
                      <AnimatedCounter value={stat.number} duration={2000 + index * 200} />
                    </motion.div>
                    <h3 className={`text-xl font-semibold text-pure-white mb-2 ${
                      locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                    }`}>
                      {stat.label}
                    </h3>
                  </div>

                  {/* Description */}
                  <p className={`text-light-gray text-center leading-relaxed ${
                    locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                  }`}>
                    {stat.description}
                  </p>

                  {/* Decorative Elements */}
                  <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4">
                    <motion.div
                      animate={{ 
                        rotate: [0, 360],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{ 
                        duration: 4,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="w-3 h-3 bg-military-green/50 rounded-full"
                    />
                  </div>

                  {/* Progress Bar */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-smoke-gray/30 rounded-b-lg overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={isInView ? { width: '100%' } : {}}
                      transition={{
                        duration: 1.5,
                        delay: 0.5 + index * 0.2,
                        ease: "easeOut"
                      }}
                      className="h-full bg-gradient-to-r from-military-green to-bright-green"
                    />
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Additional Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {/* Operational Excellence */}
          <div className="text-center">
            <div className="w-12 h-12 bg-military-green rounded-full flex items-center justify-center mx-auto mb-4 military-glow">
              <CogIcon className="w-6 h-6 text-pure-white" />
            </div>
            <h4 className={`text-lg font-semibold text-bright-green mb-2 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'التميز التشغيلي' : 'Operational Excellence'}
            </h4>
            <p className={`text-light-gray text-sm ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'أعلى معايير الأداء والكفاءة'
                : 'Highest standards of performance and efficiency'
              }
            </p>
          </div>

          {/* Strategic Partnerships */}
          <div className="text-center">
            <div className="w-12 h-12 bg-military-green rounded-full flex items-center justify-center mx-auto mb-4 military-glow">
              <UsersIcon className="w-6 h-6 text-pure-white" />
            </div>
            <h4 className={`text-lg font-semibold text-bright-green mb-2 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'الشراكات الاستراتيجية' : 'Strategic Partnerships'}
            </h4>
            <p className={`text-light-gray text-sm ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'تعاون دولي وإقليمي فعال'
                : 'Effective international and regional cooperation'
              }
            </p>
          </div>

          {/* Innovation Focus */}
          <div className="text-center">
            <div className="w-12 h-12 bg-military-green rounded-full flex items-center justify-center mx-auto mb-4 military-glow">
              <StarIcon className="w-6 h-6 text-pure-white" />
            </div>
            <h4 className={`text-lg font-semibold text-bright-green mb-2 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'التركيز على الابتكار' : 'Innovation Focus'}
            </h4>
            <p className={`text-light-gray text-sm ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'تطوير مستمر للقدرات والتقنيات'
                : 'Continuous development of capabilities and technologies'
              }
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
