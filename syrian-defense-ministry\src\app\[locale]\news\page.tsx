'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import Navigation from '@/components/layout/Navigation';
import Footer from '@/components/layout/Footer';
import NewsGrid from '@/components/sections/NewsGrid';
import FeaturedNews from '@/components/sections/FeaturedNews';
import NewsFilters from '@/components/sections/NewsFilters';

export default function NewsPage() {
  const t = useTranslations('news');
  const locale = useLocale();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  return (
    <main className="min-h-screen">
      <Navigation />
      
      {/* Page Header */}
      <section className="pt-20 pb-8 bg-deep-charcoal camo-pattern">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold text-pure-white mb-6 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {t('title')}
            </h1>
            <p className={`text-light-gray text-lg max-w-3xl mx-auto ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'آخر الأخبار والتحديثات من وزارة الدفاع السورية - تابع أحدث التطورات والإنجازات'
                : 'Latest news and updates from the Syrian Ministry of Defense - Follow the latest developments and achievements'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Featured News Section */}
      <FeaturedNews />

      {/* News Filters */}
      <NewsFilters 
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      {/* News Grid */}
      <NewsGrid 
        selectedCategory={selectedCategory}
        searchQuery={searchQuery}
      />

      <Footer />
    </main>
  );
}
