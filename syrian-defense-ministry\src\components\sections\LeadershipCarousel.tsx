'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface Leader {
  id: string;
  position: string;
  name: string;
  image: string;
  description: string;
}

export default function LeadershipCarousel() {
  const t = useTranslations('about.leadership');
  const locale = useLocale();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Mock leadership data - in real implementation, this would come from API/CMS
  const leaders: Leader[] = [
    {
      id: '1',
      position: t('positions.minister'),
      name: locale === 'ar' ? 'الفريق علي محمود عباس' : 'General <PERSON>',
      image: '/api/placeholder/300/400',
      description: locale === 'ar' 
        ? 'وزير الدفاع، خبرة 30 عاماً في الخدمة العسكرية والقيادة الاستراتيجية'
        : 'Minister of Defense, 30 years of experience in military service and strategic leadership'
    },
    {
      id: '2',
      position: t('positions.deputyMinister'),
      name: locale === 'ar' ? 'اللواء أحمد سعيد الخوري' : 'Major General Ahmad Said Al-Khoury',
      image: '/api/placeholder/300/400',
      description: locale === 'ar'
        ? 'نائب وزير الدفاع، متخصص في التطوير التقني والتحديث العسكري'
        : 'Deputy Minister of Defense, specialist in technical development and military modernization'
    },
    {
      id: '3',
      position: t('positions.chiefOfStaff'),
      name: locale === 'ar' ? 'الفريق محمد حسن النوري' : 'General Mohammad Hassan Al-Nouri',
      image: '/api/placeholder/300/400',
      description: locale === 'ar'
        ? 'رئيس الأركان العامة، قائد عسكري محنك بخبرة 25 عاماً'
        : 'Chief of General Staff, seasoned military commander with 25 years of experience'
    },
    {
      id: '4',
      position: t('positions.deputyChiefOfStaff'),
      name: locale === 'ar' ? 'اللواء خالد عمر الشامي' : 'Major General Khaled Omar Al-Shami',
      image: '/api/placeholder/300/400',
      description: locale === 'ar'
        ? 'نائب رئيس الأركان، خبير في العمليات العسكرية والتخطيط الاستراتيجي'
        : 'Deputy Chief of Staff, expert in military operations and strategic planning'
    },
    {
      id: '5',
      position: t('positions.operationsDirector'),
      name: locale === 'ar' ? 'العميد يوسف طارق الحلبي' : 'Brigadier General Youssef Tarek Al-Halabi',
      image: '/api/placeholder/300/400',
      description: locale === 'ar'
        ? 'مدير العمليات، متخصص في التكتيكات العسكرية الحديثة'
        : 'Director of Operations, specialist in modern military tactics'
    },
    {
      id: '6',
      position: t('positions.intelligenceDirector'),
      name: locale === 'ar' ? 'العميد سامر فؤاد الدمشقي' : 'Brigadier General Samer Fouad Al-Dimashqi',
      image: '/api/placeholder/300/400',
      description: locale === 'ar'
        ? 'مدير المخابرات العسكرية، خبير في الأمن القومي والاستخبارات'
        : 'Director of Military Intelligence, expert in national security and intelligence'
    }
  ];

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % leaders.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, leaders.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % leaders.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + leaders.length) % leaders.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-16 bg-steel-gray/30 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className={`text-3xl md:text-4xl font-bold text-pure-white mb-4 ${
            locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
          }`}>
            {t('title')}
          </h2>
          <p className={`text-light-gray text-lg ${
            locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
          }`}>
            {t('subtitle')}
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Main Carousel */}
          <div className="relative overflow-hidden rounded-lg bg-military-black/50 backdrop-blur-sm border border-smoke-gray/30 military-glow">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: locale === 'ar' ? -100 : 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: locale === 'ar' ? 100 : -100 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="flex flex-col lg:flex-row items-center p-8 lg:p-12"
              >
                {/* Leader Image */}
                <div className="flex-shrink-0 mb-8 lg:mb-0 lg:mr-8 rtl:lg:mr-0 rtl:lg:ml-8">
                  <div className="relative">
                    <div className="w-48 h-64 md:w-56 md:h-72 bg-smoke-gray rounded-lg overflow-hidden military-glow">
                      <div className="w-full h-full bg-gradient-to-br from-steel-gray to-smoke-gray flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-military-green rounded-full flex items-center justify-center mb-4 mx-auto">
                            <span className="text-2xl font-bold text-pure-white">
                              {leaders[currentIndex].name.charAt(0)}
                            </span>
                          </div>
                          <p className="text-light-gray text-sm">
                            {locale === 'ar' ? 'صورة رسمية' : 'Official Photo'}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    {/* Rank Indicator */}
                    <div className="absolute -top-2 -right-2 rtl:-left-2 rtl:-right-auto bg-military-green text-pure-white px-3 py-1 rounded-full text-xs font-semibold">
                      {currentIndex + 1}/{leaders.length}
                    </div>
                  </div>
                </div>

                {/* Leader Info */}
                <div className="flex-1 text-center lg:text-left rtl:lg:text-right">
                  <h3 className={`text-2xl md:text-3xl font-bold text-bright-green mb-2 ${
                    locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                  }`}>
                    {leaders[currentIndex].position}
                  </h3>
                  <h4 className={`text-xl md:text-2xl font-semibold text-pure-white mb-4 ${
                    locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                  }`}>
                    {leaders[currentIndex].name}
                  </h4>
                  <p className={`text-light-gray leading-relaxed text-lg ${
                    locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                  }`}>
                    {leaders[currentIndex].description}
                  </p>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Arrows */}
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-military-black/70 hover:bg-military-green/70 text-pure-white p-2 rounded-full transition-all duration-200 military-glow"
              aria-label={locale === 'ar' ? 'السابق' : 'Previous'}
            >
              <ChevronLeftIcon className="w-6 h-6" />
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-military-black/70 hover:bg-military-green/70 text-pure-white p-2 rounded-full transition-all duration-200 military-glow"
              aria-label={locale === 'ar' ? 'التالي' : 'Next'}
            >
              <ChevronRightIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 space-x-2 rtl:space-x-reverse">
            {leaders.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? 'bg-military-green military-glow'
                    : 'bg-smoke-gray hover:bg-bright-green/50'
                }`}
                aria-label={`${locale === 'ar' ? 'انتقل إلى الشريحة' : 'Go to slide'} ${index + 1}`}
              />
            ))}
          </div>

          {/* Auto-play Control */}
          <div className="flex justify-center mt-4">
            <button
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                isAutoPlaying
                  ? 'bg-military-green text-pure-white'
                  : 'bg-smoke-gray text-light-gray hover:bg-bright-green/50'
              }`}
            >
              {isAutoPlaying 
                ? (locale === 'ar' ? 'إيقاف التشغيل التلقائي' : 'Pause Auto-play')
                : (locale === 'ar' ? 'تشغيل تلقائي' : 'Auto-play')
              }
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
