{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(ar|en)/:path*{(\\\\.json)}?", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gQtcWOkKyTbjvKWoWIRXms23gY5ZlpwVZ3saBo/HHX0=", "__NEXT_PREVIEW_MODE_ID": "c42b3e45b5254bb68eb5a17e073a9c79", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a8e5202267dae6aaead6c11ccdffb9caa3e24ef1bed87e66d5a1b645c57a7202", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fc038ee5a3c1c2f0cbee8a8744a7810a6f24c17191239c2045ca212b80dc2f8f"}}}, "instrumentation": null, "functions": {}}