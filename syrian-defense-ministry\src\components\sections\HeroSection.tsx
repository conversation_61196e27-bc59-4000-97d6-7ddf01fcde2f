'use client';

import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  NewspaperIcon, 
  PhoneIcon, 
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';

export default function HeroSection() {
  const t = useTranslations('hero');
  const locale = useLocale();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0
    }
  };

  const emblemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with enhanced camouflage pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-military-black via-deep-charcoal to-steel-gray">
        <div className="absolute inset-0 camo-pattern opacity-30"></div>
        
        {/* Floating particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-military-green/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.2, 0.8, 0.2],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>

      {/* Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto"
      >
        {/* Ministry Emblem */}
        <motion.div
          variants={emblemVariants}
          transition={{ duration: 1, ease: "easeOut" }}
          className="mb-8 flex justify-center"
        >
          <div className="relative">
            <div className="w-32 h-32 md:w-40 md:h-40 bg-gradient-to-br from-yellow-600 via-yellow-500 to-yellow-700 rounded-full flex items-center justify-center military-glow-strong shadow-2xl">
              {/* Simplified Syrian Ministry Emblem */}
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-military-black mb-1">
                  {locale === 'ar' ? 'ود' : 'MD'}
                </div>
                <div className="text-xs md:text-sm font-semibold text-military-black">
                  {locale === 'ar' ? 'سوريا' : 'SYRIA'}
                </div>
              </div>
            </div>
            
            {/* Glow effect */}
            <motion.div
              className="absolute inset-0 w-32 h-32 md:w-40 md:h-40 bg-military-green/20 rounded-full blur-xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>

        {/* Title */}
        <motion.h1
          variants={itemVariants}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className={`text-4xl md:text-6xl lg:text-7xl font-bold mb-4 ${
            locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
          }`}
        >
          <span className="bg-gradient-to-r from-pure-white via-light-gray to-pure-white bg-clip-text text-transparent">
            {t('title')}
          </span>
        </motion.h1>

        {/* Subtitle */}
        <motion.p
          variants={itemVariants}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className={`text-xl md:text-2xl lg:text-3xl text-bright-green mb-6 font-semibold ${
            locale === 'ar' ? 'font-arabic-primary' : 'font-english-primary'
          }`}
        >
          {t('subtitle')}
        </motion.p>

        {/* Description */}
        <motion.p
          variants={itemVariants}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className={`text-lg md:text-xl text-light-gray mb-12 max-w-4xl mx-auto leading-relaxed ${
            locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
          }`}
        >
          {t('description')}
        </motion.p>

        {/* Call to Action Buttons */}
        <motion.div
          variants={itemVariants}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <Link
            href={`/${locale}/news`}
            className="group flex items-center space-x-3 rtl:space-x-reverse bg-military-green hover:bg-bright-green text-pure-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 military-glow hover:military-glow-strong"
          >
            <NewspaperIcon className="w-5 h-5" />
            <span>{t('cta.news')}</span>
          </Link>

          <Link
            href={`/${locale}/contact`}
            className="group flex items-center space-x-3 rtl:space-x-reverse bg-transparent border-2 border-steel-gray hover:border-bright-green text-light-gray hover:text-bright-green px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
          >
            <PhoneIcon className="w-5 h-5" />
            <span>{t('cta.contact')}</span>
          </Link>

          <Link
            href={`/${locale}/emergency`}
            className="group flex items-center space-x-3 rtl:space-x-reverse bg-warning-red hover:bg-red-700 text-pure-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-red-900/50"
          >
            <ExclamationTriangleIcon className="w-5 h-5" />
            <span>{t('cta.emergency')}</span>
          </Link>
        </motion.div>
      </motion.div>

      {/* Bottom gradient overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-deep-charcoal to-transparent"></div>
    </section>
  );
}
