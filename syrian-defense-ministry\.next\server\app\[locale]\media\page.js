(()=>{var e={};e.id=366,e.ids=[366],e.modules={419:(e,t,r)=>{Promise.resolve().then(r.bind(r,5196)),Promise.resolve().then(r.bind(r,2830)),Promise.resolve().then(r.bind(r,3197))},563:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(1120),n=r(4604),a=(0,s.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:s,namespace:a,onError:o=n.g,...i}){return function({messages:e,namespace:t,...r},s){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...i,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":s},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),o=r(8692);function i(...[e]){return a((0,o.A)("useTranslations"),e)}},747:(e,t,r)=>{Promise.resolve().then(r.bind(r,994)),Promise.resolve().then(r.bind(r,4712)),Promise.resolve().then(r.bind(r,1195))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1195:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\src\\\\components\\\\layout\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Navigation.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(5239),n=r(8088),a=r(8170),o=r.n(a),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["media",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4586)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\media\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1434)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\media\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/media/page",pathname:"/[locale]/media",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3685:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(8692);function n(){return(0,s.A)("useLocale").locale}},3873:e=>{"use strict";e.exports=require("path")},4586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(7413),n=r(563),a=r(3685),o=r(1195),i=r(4712);function l(){(0,n.A)();let e=(0,a.A)();return(0,s.jsxs)("main",{className:"min-h-screen",children:[(0,s.jsx)(o.default,{}),(0,s.jsx)("section",{className:"pt-20 pb-16 bg-deep-charcoal camo-pattern",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:`text-4xl md:text-5xl lg:text-6xl font-bold text-pure-white mb-6 ${"ar"===e?"font-arabic-headings":"font-english-primary"}`,children:"ar"===e?"معرض الوسائط":"Media Gallery"}),(0,s.jsx)("p",{className:`text-light-gray text-lg max-w-3xl mx-auto ${"ar"===e?"font-arabic-secondary":"font-english-secondary"}`,children:"ar"===e?"معرض الصور والفيديوهات الرسمية لأنشطة وفعاليات وزارة الدفاع السورية":"Official photo and video gallery of Syrian Ministry of Defense activities and events"})]})})}),(0,s.jsx)("section",{className:"py-16 bg-steel-gray/20",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)("div",{className:"bg-military-black/50 backdrop-blur-sm rounded-lg p-12 border border-smoke-gray/30 military-glow",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-military-green rounded-full flex items-center justify-center mx-auto mb-6 military-glow-strong",children:(0,s.jsx)("span",{className:"text-3xl font-bold text-pure-white",children:"ar"===e?"و":"M"})}),(0,s.jsx)("h2",{className:`text-3xl font-bold text-pure-white mb-4 ${"ar"===e?"font-arabic-headings":"font-english-primary"}`,children:"ar"===e?"قريباً":"Coming Soon"}),(0,s.jsx)("p",{className:`text-light-gray text-lg leading-relaxed ${"ar"===e?"font-arabic-secondary":"font-english-secondary"}`,children:"ar"===e?"نعمل حالياً على تطوير معرض الوسائط التفاعلي الذي سيضم مجموعة شاملة من الصور والفيديوهات الرسمية.":"We are currently developing an interactive media gallery that will feature a comprehensive collection of official photos and videos."})]})})}),(0,s.jsx)(i.default,{})]})}},4712:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Footer.tsx","default")},8692:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(9769),n=r(1120),a=r.t(n,2)["use".trim()];function o(e){var t=(0,s.A)();try{return a(t)}catch(t){throw t instanceof TypeError&&t.message.includes("Cannot read properties of null (reading 'use')")?Error(`\`${e}\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`,{cause:t}):t}}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,825,625,768,216],()=>r(3287));module.exports=s})();