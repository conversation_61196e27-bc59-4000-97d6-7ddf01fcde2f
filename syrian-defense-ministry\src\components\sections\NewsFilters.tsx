'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

interface NewsFiltersProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export default function NewsFilters({
  selectedCategory,
  onCategoryChange,
  searchQuery,
  onSearchChange
}: NewsFiltersProps) {
  const t = useTranslations('news');
  const locale = useLocale();
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [sortBy, setSortBy] = useState('date');

  const categories = [
    { key: 'all', label: locale === 'ar' ? 'جميع الأخبار' : 'All News' },
    { key: 'military', label: t('categories.military') },
    { key: 'training', label: t('categories.training') },
    { key: 'international', label: t('categories.international') },
    { key: 'domestic', label: t('categories.domestic') }
  ];

  const sortOptions = [
    { key: 'date', label: locale === 'ar' ? 'الأحدث' : 'Latest' },
    { key: 'relevance', label: locale === 'ar' ? 'الأكثر صلة' : 'Most Relevant' },
    { key: 'popular', label: locale === 'ar' ? 'الأكثر قراءة' : 'Most Read' }
  ];

  const clearFilters = () => {
    onCategoryChange('all');
    onSearchChange('');
    setSortBy('date');
  };

  const hasActiveFilters = selectedCategory !== 'all' || searchQuery !== '' || sortBy !== 'date';

  return (
    <section className="py-8 bg-military-black/30 backdrop-blur-sm border-y border-smoke-gray/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          {/* Search Bar */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-light-gray" />
              <input
                type="text"
                placeholder={locale === 'ar' ? 'البحث في الأخبار...' : 'Search news...'}
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className={`w-full bg-steel-gray/50 border border-smoke-gray/30 rounded-lg py-3 pl-10 pr-4 rtl:pl-4 rtl:pr-10 text-pure-white placeholder-light-gray focus:outline-none focus:border-military-green focus:ring-2 focus:ring-military-green/20 transition-all duration-200 ${
                  locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                }`}
              />
              {searchQuery && (
                <button
                  onClick={() => onSearchChange('')}
                  className="absolute right-3 rtl:right-auto rtl:left-3 top-1/2 transform -translate-y-1/2 text-light-gray hover:text-pure-white transition-colors duration-200"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* Filter Toggle (Mobile) */}
          <div className="lg:hidden">
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsFiltersOpen(!isFiltersOpen)}
              className="flex items-center space-x-2 rtl:space-x-reverse bg-military-green hover:bg-bright-green text-pure-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              <FunnelIcon className="w-5 h-5" />
              <span>{locale === 'ar' ? 'تصفية' : 'Filters'}</span>
              {hasActiveFilters && (
                <span className="bg-warning-red text-pure-white text-xs px-2 py-1 rounded-full">
                  {locale === 'ar' ? 'نشط' : 'Active'}
                </span>
              )}
            </motion.button>
          </div>

          {/* Desktop Filters */}
          <div className="hidden lg:flex items-center space-x-6 rtl:space-x-reverse">
            {/* Category Filters */}
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {categories.map((category) => (
                <motion.button
                  key={category.key}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => onCategoryChange(category.key)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    selectedCategory === category.key
                      ? 'bg-military-green text-pure-white military-glow'
                      : 'bg-steel-gray/50 text-light-gray hover:bg-smoke-gray hover:text-pure-white'
                  }`}
                >
                  {category.label}
                </motion.button>
              ))}
            </div>

            {/* Sort Options */}
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <AdjustmentsHorizontalIcon className="w-5 h-5 text-light-gray" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className={`bg-steel-gray/50 border border-smoke-gray/30 rounded-lg py-2 px-3 text-pure-white focus:outline-none focus:border-military-green transition-colors duration-200 ${
                  locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                }`}
              >
                {sortOptions.map((option) => (
                  <option key={option.key} value={option.key} className="bg-steel-gray">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={clearFilters}
                className="text-warning-red hover:text-pure-white font-medium transition-colors duration-200"
              >
                {locale === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
              </motion.button>
            )}
          </div>
        </div>

        {/* Mobile Filters Panel */}
        <AnimatePresence>
          {isFiltersOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="lg:hidden mt-6 bg-steel-gray/30 rounded-lg p-6 border border-smoke-gray/30"
            >
              {/* Category Filters */}
              <div className="mb-6">
                <h3 className={`text-lg font-semibold text-pure-white mb-3 ${
                  locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                }`}>
                  {locale === 'ar' ? 'الفئات' : 'Categories'}
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  {categories.map((category) => (
                    <motion.button
                      key={category.key}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => onCategoryChange(category.key)}
                      className={`px-3 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
                        selectedCategory === category.key
                          ? 'bg-military-green text-pure-white'
                          : 'bg-smoke-gray/50 text-light-gray hover:bg-smoke-gray hover:text-pure-white'
                      }`}
                    >
                      {category.label}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Sort Options */}
              <div className="mb-6">
                <h3 className={`text-lg font-semibold text-pure-white mb-3 ${
                  locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                }`}>
                  {locale === 'ar' ? 'ترتيب حسب' : 'Sort By'}
                </h3>
                <div className="grid grid-cols-1 gap-2">
                  {sortOptions.map((option) => (
                    <motion.button
                      key={option.key}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSortBy(option.key)}
                      className={`px-3 py-2 rounded-lg font-medium text-sm text-left rtl:text-right transition-all duration-200 ${
                        sortBy === option.key
                          ? 'bg-military-green text-pure-white'
                          : 'bg-smoke-gray/50 text-light-gray hover:bg-smoke-gray hover:text-pure-white'
                      }`}
                    >
                      {option.label}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={clearFilters}
                  className="w-full bg-warning-red hover:bg-warning-red/80 text-pure-white py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  {locale === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'}
                </motion.button>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 flex flex-wrap items-center gap-2"
          >
            <span className={`text-light-gray text-sm ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' ? 'الفلاتر النشطة:' : 'Active filters:'}
            </span>
            
            {selectedCategory !== 'all' && (
              <span className="bg-military-green/20 text-military-green px-2 py-1 rounded-full text-sm">
                {categories.find(c => c.key === selectedCategory)?.label}
              </span>
            )}
            
            {searchQuery && (
              <span className="bg-bright-green/20 text-bright-green px-2 py-1 rounded-full text-sm">
                "{searchQuery}"
              </span>
            )}
            
            {sortBy !== 'date' && (
              <span className="bg-smoke-gray/50 text-light-gray px-2 py-1 rounded-full text-sm">
                {sortOptions.find(s => s.key === sortBy)?.label}
              </span>
            )}
          </motion.div>
        )}
      </div>
    </section>
  );
}
