'use client';

import { useState, useRef } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion, useInView } from 'framer-motion';
import { CalendarIcon, StarIcon } from '@heroicons/react/24/outline';

interface TimelineEvent {
  year: string;
  title: string;
  description: string;
}

export default function HistoricalTimeline() {
  const t = useTranslations('about.timeline');
  const locale = useLocale();
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  const events: TimelineEvent[] = t.raw('events');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1
    }
  };

  const lineVariants = {
    hidden: { scaleY: 0 },
    visible: {
      scaleY: 1
    }
  };

  return (
    <section className="py-16 bg-deep-charcoal camo-pattern">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2 
            initial={{ opacity: 0, y: -20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6 }}
            className={`text-3xl md:text-4xl font-bold text-pure-white mb-4 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}
          >
            {t('title')}
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: -10 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={`text-light-gray text-lg ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}
          >
            {t('subtitle')}
          </motion.p>
        </div>

        {/* Timeline Container */}
        <div ref={containerRef} className="relative">
          {/* Central Timeline Line */}
          <motion.div
            variants={lineVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            transition={{ duration: 1.5, ease: "easeInOut", delay: 0.3 }}
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-military-green via-bright-green to-military-green h-full origin-top"
            style={{ 
              background: 'linear-gradient(to bottom, #2d5016, #4a7c59, #2d5016)',
              boxShadow: '0 0 20px rgba(45, 80, 22, 0.5)'
            }}
          />

          {/* Timeline Events */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="space-y-12"
          >
            {events.map((event, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className={`relative flex items-center ${
                  index % 2 === 0 ? 'justify-start' : 'justify-end'
                }`}
              >
                {/* Timeline Node */}
                <motion.div
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  className="absolute left-1/2 transform -translate-x-1/2 z-10 cursor-pointer"
                  onClick={() => setSelectedEvent(selectedEvent === index ? null : index)}
                >
                  <div className="w-6 h-6 bg-military-green rounded-full border-4 border-pure-white military-glow-strong flex items-center justify-center">
                    <motion.div
                      animate={{ 
                        scale: selectedEvent === index ? [1, 1.5, 1] : 1,
                        rotate: selectedEvent === index ? [0, 180, 360] : 0
                      }}
                      transition={{ duration: 0.5 }}
                    >
                      <StarIcon className="w-3 h-3 text-pure-white" />
                    </motion.div>
                  </div>
                </motion.div>

                {/* Event Card */}
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  className={`w-full max-w-md ${
                    index % 2 === 0 ? 'mr-auto pr-8' : 'ml-auto pl-8'
                  }`}
                >
                  <div className="bg-steel-gray/70 backdrop-blur-sm rounded-lg p-6 border border-smoke-gray/30 military-glow hover:military-glow-strong transition-all duration-300">
                    {/* Year Badge */}
                    <div className={`flex items-center mb-3 ${
                      index % 2 === 0 ? 'justify-start' : 'justify-end'
                    }`}>
                      <div className="bg-military-green text-pure-white px-4 py-2 rounded-full flex items-center space-x-2 rtl:space-x-reverse">
                        <CalendarIcon className="w-4 h-4" />
                        <span className="font-bold text-lg">{event.year}</span>
                      </div>
                    </div>

                    {/* Event Title */}
                    <h3 className={`text-xl font-bold text-bright-green mb-3 ${
                      locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                    } ${index % 2 === 0 ? 'text-left rtl:text-right' : 'text-right rtl:text-left'}`}>
                      {event.title}
                    </h3>

                    {/* Event Description */}
                    <p className={`text-light-gray leading-relaxed ${
                      locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                    } ${index % 2 === 0 ? 'text-left rtl:text-right' : 'text-right rtl:text-left'}`}>
                      {event.description}
                    </p>

                    {/* Expand Button */}
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedEvent(selectedEvent === index ? null : index)}
                      className={`mt-4 text-military-green hover:text-bright-green font-medium text-sm transition-colors duration-200 ${
                        index % 2 === 0 ? 'text-left rtl:text-right' : 'text-right rtl:text-left'
                      }`}
                    >
                      {selectedEvent === index 
                        ? (locale === 'ar' ? 'إخفاء التفاصيل' : 'Hide Details')
                        : (locale === 'ar' ? 'عرض التفاصيل' : 'Show Details')
                      }
                    </motion.button>

                    {/* Expanded Content */}
                    <motion.div
                      initial={false}
                      animate={{ 
                        height: selectedEvent === index ? 'auto' : 0,
                        opacity: selectedEvent === index ? 1 : 0
                      }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      {selectedEvent === index && (
                        <div className="mt-4 pt-4 border-t border-smoke-gray/30">
                          <div className="bg-military-black/30 rounded-lg p-4">
                            <p className={`text-light-gray text-sm leading-relaxed ${
                              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                            }`}>
                              {locale === 'ar' 
                                ? 'هذا الحدث يمثل محطة مهمة في تاريخ وزارة الدفاع السورية، حيث ساهم في تعزيز قدراتها الدفاعية وترسيخ دورها في حماية الوطن والمواطن.'
                                : 'This event represents an important milestone in the history of the Syrian Ministry of Defense, contributing to strengthening its defensive capabilities and consolidating its role in protecting the nation and citizens.'
                              }
                            </p>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  </div>
                </motion.div>

                {/* Connection Line to Node */}
                <motion.div
                  initial={{ scaleX: 0 }}
                  animate={isInView ? { scaleX: 1 } : {}}
                  transition={{ duration: 0.8, delay: 0.5 + index * 0.1 }}
                  className={`absolute top-1/2 w-8 h-0.5 bg-military-green origin-${
                    index % 2 === 0 ? 'right' : 'left'
                  } ${
                    index % 2 === 0 
                      ? 'left-1/2 ml-3' 
                      : 'right-1/2 mr-3'
                  }`}
                />
              </motion.div>
            ))}
          </motion.div>

          {/* Timeline End Marker */}
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 1.5 }}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-4"
          >
            <div className="w-8 h-8 bg-gradient-to-br from-military-green to-bright-green rounded-full border-4 border-pure-white military-glow-strong flex items-center justify-center">
              <div className="w-2 h-2 bg-pure-white rounded-full"></div>
            </div>
          </motion.div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 1.8 }}
          className="text-center mt-16"
        >
          <p className={`text-light-gray text-lg mb-6 ${
            locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
          }`}>
            {locale === 'ar' 
              ? 'تاريخ حافل بالإنجازات والتضحيات في سبيل الوطن'
              : 'A rich history of achievements and sacrifices for the homeland'
            }
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-military-green hover:bg-bright-green text-pure-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 military-glow"
          >
            {locale === 'ar' ? 'اعرف المزيد عن تاريخنا' : 'Learn More About Our History'}
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
