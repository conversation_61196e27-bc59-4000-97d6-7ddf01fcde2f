'use client';

import { useState, useMemo } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CalendarIcon,
  ClockIcon,
  ArrowRightIcon,
  EyeIcon,
  ShareIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface NewsItem {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  date: string;
  readTime: string;
  views: number;
  image: string;
  urgent: boolean;
}

interface NewsGridProps {
  selectedCategory: string;
  searchQuery: string;
}

export default function NewsGrid({ selectedCategory, searchQuery }: NewsGridProps) {
  const t = useTranslations('news');
  const locale = useLocale();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 9;

  // Mock news data - in real implementation, this would come from API/CMS
  const allNews: NewsItem[] = [
    {
      id: '2',
      title: locale === 'ar' 
        ? 'تخريج دفعة جديدة من ضباط الكلية الحربية بحضور قيادات عسكرية رفيعة'
        : 'Graduation of New Class of Military Academy Officers in Presence of High Military Leadership',
      excerpt: locale === 'ar'
        ? 'شهدت الكلية الحربية حفل تخريج دفعة جديدة من الضباط المتميزين الذين أكملوا برنامج التدريب المتقدم'
        : 'The Military Academy witnessed the graduation ceremony of a new class of distinguished officers who completed the advanced training program',
      category: locale === 'ar' ? 'التدريب' : 'Training',
      date: locale === 'ar' ? '14 ديسمبر 2024' : 'December 14, 2024',
      readTime: locale === 'ar' ? '4 دقائق' : '4 min read',
      views: 1250,
      image: '/api/placeholder/400/300',
      urgent: false
    },
    {
      id: '3',
      title: locale === 'ar'
        ? 'انطلاق مناورات عسكرية مشتركة لتعزيز الجاهزية القتالية للقوات المسلحة'
        : 'Launch of Joint Military Exercises to Enhance Combat Readiness of Armed Forces',
      excerpt: locale === 'ar'
        ? 'بدأت القوات المسلحة السورية سلسلة من المناورات العسكرية المشتركة لاختبار وتطوير القدرات القتالية'
        : 'Syrian Armed Forces began a series of joint military exercises to test and develop combat capabilities',
      category: locale === 'ar' ? 'العمليات العسكرية' : 'Military Operations',
      date: locale === 'ar' ? '13 ديسمبر 2024' : 'December 13, 2024',
      readTime: locale === 'ar' ? '6 دقائق' : '6 min read',
      views: 2100,
      image: '/api/placeholder/400/300',
      urgent: true
    },
    {
      id: '4',
      title: locale === 'ar'
        ? 'توقيع اتفاقية تعاون في مجال التدريب العسكري مع دولة صديقة'
        : 'Signing of Military Training Cooperation Agreement with Friendly Nation',
      excerpt: locale === 'ar'
        ? 'وقعت وزارة الدفاع اتفاقية تعاون استراتيجي في مجال التدريب العسكري وتبادل الخبرات'
        : 'Ministry of Defense signed a strategic cooperation agreement in military training and expertise exchange',
      category: locale === 'ar' ? 'التعاون الدولي' : 'International Cooperation',
      date: locale === 'ar' ? '12 ديسمبر 2024' : 'December 12, 2024',
      readTime: locale === 'ar' ? '3 دقائق' : '3 min read',
      views: 890,
      image: '/api/placeholder/400/300',
      urgent: false
    },
    {
      id: '5',
      title: locale === 'ar'
        ? 'إطلاق برنامج التحديث التقني للمعدات العسكرية والأنظمة الدفاعية'
        : 'Launch of Technical Modernization Program for Military Equipment and Defense Systems',
      excerpt: locale === 'ar'
        ? 'أعلنت الوزارة عن برنامج شامل لتحديث وتطوير المعدات العسكرية باستخدام أحدث التقنيات'
        : 'Ministry announced comprehensive program to modernize and develop military equipment using latest technologies',
      category: locale === 'ar' ? 'الشؤون المحلية' : 'Domestic Affairs',
      date: locale === 'ar' ? '11 ديسمبر 2024' : 'December 11, 2024',
      readTime: locale === 'ar' ? '5 دقائق' : '5 min read',
      views: 1560,
      image: '/api/placeholder/400/300',
      urgent: false
    },
    {
      id: '6',
      title: locale === 'ar'
        ? 'افتتاح مركز جديد للتدريب المتخصص في الأمن السيبراني'
        : 'Opening of New Specialized Cybersecurity Training Center',
      excerpt: locale === 'ar'
        ? 'تم افتتاح مركز متطور للتدريب على الأمن السيبراني لتعزيز قدرات الدفاع الرقمي'
        : 'Advanced cybersecurity training center opened to enhance digital defense capabilities',
      category: locale === 'ar' ? 'التدريب' : 'Training',
      date: locale === 'ar' ? '10 ديسمبر 2024' : 'December 10, 2024',
      readTime: locale === 'ar' ? '4 دقائق' : '4 min read',
      views: 980,
      image: '/api/placeholder/400/300',
      urgent: false
    },
    {
      id: '7',
      title: locale === 'ar'
        ? 'مشاركة وفد عسكري سوري في مؤتمر الأمن الإقليمي'
        : 'Syrian Military Delegation Participates in Regional Security Conference',
      excerpt: locale === 'ar'
        ? 'شارك وفد عسكري رفيع المستوى في مؤتمر الأمن الإقليمي لمناقشة التحديات الأمنية المشتركة'
        : 'High-level military delegation participated in regional security conference to discuss common security challenges',
      category: locale === 'ar' ? 'التعاون الدولي' : 'International Cooperation',
      date: locale === 'ar' ? '9 ديسمبر 2024' : 'December 9, 2024',
      readTime: locale === 'ar' ? '3 دقائق' : '3 min read',
      views: 720,
      image: '/api/placeholder/400/300',
      urgent: false
    }
  ];

  // Filter and search logic
  const filteredNews = useMemo(() => {
    let filtered = allNews;

    // Filter by category
    if (selectedCategory !== 'all') {
      const categoryMap: { [key: string]: string } = {
        'military': locale === 'ar' ? 'العمليات العسكرية' : 'Military Operations',
        'training': locale === 'ar' ? 'التدريب' : 'Training',
        'international': locale === 'ar' ? 'التعاون الدولي' : 'International Cooperation',
        'domestic': locale === 'ar' ? 'الشؤون المحلية' : 'Domestic Affairs'
      };
      filtered = filtered.filter(news => news.category === categoryMap[selectedCategory]);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(news =>
        news.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        news.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [allNews, selectedCategory, searchQuery, locale]);

  // Pagination
  const totalPages = Math.ceil(filteredNews.length / itemsPerPage);
  const paginatedNews = filteredNews.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1
    }
  };

  return (
    <section className="py-16 bg-deep-charcoal camo-pattern">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Results Summary */}
        <div className="mb-8">
          <p className={`text-light-gray ${
            locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
          }`}>
            {locale === 'ar' 
              ? `عرض ${paginatedNews.length} من أصل ${filteredNews.length} خبر`
              : `Showing ${paginatedNews.length} of ${filteredNews.length} news articles`
            }
          </p>
        </div>

        {/* News Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`${selectedCategory}-${searchQuery}-${currentPage}`}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {paginatedNews.map((news) => (
              <motion.div
                key={news.id}
                variants={cardVariants}
                transition={{ duration: 0.6, ease: "easeOut" }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group"
              >
                <Link href={`/${locale}/news/${news.id}`}>
                  <div className="bg-steel-gray/50 backdrop-blur-sm rounded-lg overflow-hidden border border-smoke-gray/30 military-glow hover:military-glow-strong transition-all duration-300 h-full flex flex-col">
                    {/* Image */}
                    <div className="relative h-48 overflow-hidden">
                      <div className="w-full h-full bg-gradient-to-br from-smoke-gray to-steel-gray flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-military-green rounded-full flex items-center justify-center mb-2 mx-auto">
                            <span className="text-lg font-bold text-pure-white">
                              {news.title.charAt(0)}
                            </span>
                          </div>
                          <p className="text-light-gray text-xs">
                            {locale === 'ar' ? 'صورة الخبر' : 'News Image'}
                          </p>
                        </div>
                      </div>
                      
                      {/* Urgent Badge */}
                      {news.urgent && (
                        <div className="absolute top-3 left-3 rtl:left-auto rtl:right-3 bg-warning-red text-pure-white px-2 py-1 rounded-full text-xs font-bold">
                          {locale === 'ar' ? 'عاجل' : 'URGENT'}
                        </div>
                      )}

                      {/* Category Badge */}
                      <div className="absolute bottom-3 left-3 rtl:left-auto rtl:right-3 bg-military-green/90 text-pure-white px-2 py-1 rounded-full text-xs font-semibold">
                        {news.category}
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6 flex-1 flex flex-col">
                      {/* Meta Info */}
                      <div className="flex items-center justify-between text-light-gray text-sm mb-3">
                        <div className="flex items-center space-x-4 rtl:space-x-reverse">
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <CalendarIcon className="w-4 h-4" />
                            <span>{news.date}</span>
                          </div>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <ClockIcon className="w-4 h-4" />
                            <span>{news.readTime}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <EyeIcon className="w-4 h-4" />
                          <span>{news.views.toLocaleString()}</span>
                        </div>
                      </div>

                      {/* Title */}
                      <h3 className={`text-lg font-bold text-pure-white mb-3 group-hover:text-bright-green transition-colors duration-300 line-clamp-2 ${
                        locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                      }`}>
                        {news.title}
                      </h3>

                      {/* Excerpt */}
                      <p className={`text-light-gray leading-relaxed mb-4 flex-1 line-clamp-3 ${
                        locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
                      }`}>
                        {news.excerpt}
                      </p>

                      {/* Footer */}
                      <div className="flex items-center justify-between">
                        <motion.div
                          whileHover={{ x: locale === 'ar' ? -5 : 5 }}
                          className="flex items-center text-military-green hover:text-bright-green font-semibold text-sm transition-colors duration-200"
                        >
                          <span className="mr-2 rtl:mr-0 rtl:ml-2">
                            {t('readMore')}
                          </span>
                          <ArrowRightIcon className={`w-4 h-4 ${locale === 'ar' ? 'rotate-180' : ''}`} />
                        </motion.div>

                        <button className="text-light-gray hover:text-bright-green transition-colors duration-200">
                          <ShareIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* No Results */}
        {filteredNews.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <div className="w-24 h-24 bg-smoke-gray rounded-full flex items-center justify-center mx-auto mb-6">
              <MagnifyingGlassIcon className="w-12 h-12 text-light-gray" />
            </div>
            <h3 className={`text-xl font-semibold text-pure-white mb-2 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {locale === 'ar' ? 'لا توجد نتائج' : 'No Results Found'}
            </h3>
            <p className={`text-light-gray ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'جرب تغيير معايير البحث أو الفلاتر'
                : 'Try adjusting your search criteria or filters'
              }
            </p>
          </motion.div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-center mt-12"
          >
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {/* Previous Button */}
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-4 py-2 bg-steel-gray/50 text-light-gray rounded-lg hover:bg-military-green hover:text-pure-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {locale === 'ar' ? 'السابق' : 'Previous'}
              </button>

              {/* Page Numbers */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    currentPage === page
                      ? 'bg-military-green text-pure-white military-glow'
                      : 'bg-steel-gray/50 text-light-gray hover:bg-smoke-gray hover:text-pure-white'
                  }`}
                >
                  {page}
                </button>
              ))}

              {/* Next Button */}
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-4 py-2 bg-steel-gray/50 text-light-gray rounded-lg hover:bg-military-green hover:text-pure-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {locale === 'ar' ? 'التالي' : 'Next'}
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
}
