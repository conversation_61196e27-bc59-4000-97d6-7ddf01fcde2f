{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/defence%20minister%20of%20syria/syrian-defense-ministry/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\n\nexport const metadata: Metadata = {\n  title: \"Syrian Ministry of Defense | وزارة الدفاع السورية\",\n  description: \"Official website of the Ministry of Defense of the Syrian Arab Republic | الموقع الرسمي لوزارة الدفاع في الجمهورية العربية السورية\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,OAAO;AACT", "debugId": null}}]}